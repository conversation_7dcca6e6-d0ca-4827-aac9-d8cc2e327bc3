{"name": "fci-portal-premium", "private": true, "version": "1.0.0", "type": "module", "description": "Premium University Portal for Faculty of Computer and Information Sciences", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@fortawesome/fontawesome-free": "^6.7.2", "@fortawesome/fontawesome-svg-core": "^6.7.2", "@fortawesome/free-solid-svg-icons": "^6.7.2", "@fortawesome/react-fontawesome": "^0.2.2", "@heroicons/react": "^2.2.0", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-toast": "^1.2.14", "axios": "^1.10.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cors": "^2.8.5", "flowbite": "^3.1.2", "flowbite-react": "^0.10.2", "formik": "^2.4.6", "framer-motion": "^12.22.0", "js-cookie": "^3.0.5", "live-server": "^1.2.2", "lucide-react": "^0.503.0", "react": "^19.0.0", "react-countup": "^6.5.3", "react-dom": "^19.0.0", "react-icons": "^5.5.0", "react-intersection-observer": "^9.16.0", "react-router-dom": "^7.6.2", "react-tooltip": "^5.28.1", "swiper": "^11.2.6", "tailwind-merge": "^3.3.1", "tailwindcss-animate": "^1.0.7", "yup": "^1.6.1"}, "devDependencies": {"@eslint/js": "^9.21.0", "@types/axios": "^0.9.36", "@types/react": "^19.0.10", "@types/react-dom": "^19.0.4", "@vitejs/plugin-react": "^4.3.4", "autoprefixer": "^10.4.21", "eslint": "^9.21.0", "eslint-plugin-react-hooks": "^5.1.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^15.15.0", "postcss": "^8.5.3", "tailwindcss": "^3.4.17", "vite": "^6.2.0"}}