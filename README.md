# FCI Portal - Premium University Platform

A cutting-edge, premium React-based university portal application for the Faculty of Computer and Information Sciences (FCI). Built with modern technologies and premium UI/UX design principles.

## ✨ Premium Features

- � **Premium Design System** - Modern glassmorphism and gradient designs
- 🎭 **Smooth Animations** - Framer Motion powered micro-interactions
- 🌓 **Advanced Dark/Light Mode** - System preference detection with smooth transitions
- 📱 **Premium Responsive Design** - Pixel-perfect on all devices
- � **Modern UI Components** - Custom-built with Radix UI primitives
- 🎯 **Interactive Elements** - Hover effects, micro-animations, and feedback
- � **Animated Statistics** - CountUp animations with intersection observers
- 🎪 **Premium Carousels** - Enhanced Swiper.js with custom navigation
- � **Gradient Backgrounds** - Dynamic, layered gradient backgrounds
- 💎 **Glass Morphism** - Modern frosted glass effects throughout

## 🛠 Tech Stack

- **Frontend**: React 19, Vite
- **Styling**: Tailwind CSS with custom animations
- **UI Components**: Custom premium components with Radix UI
- **Animations**: Framer Motion
- **Icons**: Font Awesome, Heroicons, React Icons, Lucide React
- **Carousel**: Swiper.js with premium styling
- **Forms**: Formik + Yup validation
- **Routing**: React Router DOM
- **Utils**: clsx, tailwind-merge, class-variance-authority

## Getting Started

1. Install dependencies:

```bash
npm install
```

2. Start development server:

```bash
npm run dev
```

3. Build for production:

```bash
npm run build
```

## Project Structure

```
src/
├── components/          # React components
│   ├── About/          # About page
│   ├── BookPage/       # Book resources
│   ├── ContactUs/      # Contact form
│   ├── Courses/        # Course listings
│   ├── Department/     # Department pages
│   ├── Home/           # Landing page
│   ├── Login/          # Authentication
│   ├── Navbar/         # Navigation
│   └── ...
├── assets/             # Static assets
│   └── image/          # Images and icons
├── App.jsx             # Main app component
└── main.jsx            # Entry point
```

## Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run preview` - Preview production build
- `npm run lint` - Run ESLint
