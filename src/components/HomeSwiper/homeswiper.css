/* Simple Swiper Styles */

/* Container */
.simple-swiper {
  padding-bottom: 50px !important;
}

/* Swiper Slides */
.simple-swiper .swiper-slide {
  height: auto !important;
}

/* Swiper Wrapper */
.simple-swiper .swiper-wrapper {
  align-items: stretch;
}

/* Swiper Pagination */
.simple-swiper .swiper-pagination {
  bottom: 0 !important;
}

.simple-swiper .swiper-pagination-bullet {
  width: 10px;
  height: 10px;
  background: rgba(156, 163, 175, 0.5);
  opacity: 1;
  margin: 0 4px;
  transition: all 0.3s ease;
}

.simple-swiper .swiper-pagination-bullet-active {
  background: #3b82f6;
  transform: scale(1.2);
}

/* Navigation Buttons */
.swiper-button-prev,
.swiper-button-next {
  width: 40px !important;
  height: 40px !important;
  margin-top: -20px !important;
}

.swiper-button-prev:after,
.swiper-button-next:after {
  display: none !important;
}

/* Event Cards */
.simple-swiper .swiper-slide > div {
  height: 100%;
  display: flex;
  flex-direction: column;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .simple-swiper {
    padding-bottom: 40px !important;
  }

  .swiper-button-prev,
  .swiper-button-next {
    display: none !important;
  }
}

/* Swiper Slides */
.premium-swiper .swiper-slide {
  transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
  opacity: 0.7;
  transform: scale(0.9);
}

.premium-swiper .swiper-slide-active {
  opacity: 1;
  transform: scale(1);
}

.premium-swiper .swiper-slide-next,
.premium-swiper .swiper-slide-prev {
  opacity: 0.8;
  transform: scale(0.95);
}

/* Custom Pagination */
.swiper-pagination-custom .swiper-pagination-bullet {
  width: 12px;
  height: 12px;
  background: rgba(156, 163, 175, 0.5);
  opacity: 1;
  margin: 0 6px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
}
.swiper.swiper-coverflow {
  overflow: hidden;
}
.swiper-pagination-custom .swiper-pagination-bullet-active {
  background: linear-gradient(135deg, #3b82f6, #8b5cf6);
  transform: scale(1.3);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
}

.swiper-pagination-custom .swiper-pagination-bullet:hover {
  background: rgba(59, 130, 246, 0.7);
  transform: scale(1.1);
}

/* Custom Navigation Buttons */
.swiper-button-next-custom,
.swiper-button-prev-custom {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  backdrop-filter: blur(16px);
  -webkit-backdrop-filter: blur(16px);
}

.swiper-button-next-custom:hover,
.swiper-button-prev-custom:hover {
  transform: scale(1.1);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.swiper-button-next-custom:active,
.swiper-button-prev-custom:active {
  transform: scale(0.95);
}

/* Coverflow Effect Enhancements */
.premium-swiper .swiper-slide-shadow-left,
.premium-swiper .swiper-slide-shadow-right {
  background: linear-gradient(135deg, rgba(0, 0, 0, 0.3), rgba(0, 0, 0, 0.1));
  border-radius: 24px;
}

/* Glass Morphism Effects */
.glass-card {
  backdrop-filter: blur(16px);
  -webkit-backdrop-filter: blur(16px);
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Animations */
@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInScale {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes pulseGlow {
  0%,
  100% {
    opacity: 1;
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.4);
  }
  50% {
    opacity: 0.8;
    box-shadow: 0 0 30px rgba(59, 130, 246, 0.6);
  }
}

/* Card Hover Effects */
.event-card {
  transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
}

.event-card:hover {
  transform: translateY(-10px) scale(1.02);
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
}

/* Responsive Design */
@media (max-width: 768px) {
  .premium-swiper {
    padding-bottom: 40px !important;
  }

  .swiper-button-next-custom,
  .swiper-button-prev-custom {
    display: none;
  }

  .swiper-pagination-custom {
    position: relative;
    bottom: 0;
    margin-top: 20px;
  }
}

/* Custom Scrollbar for Timeline */
.timeline-container::-webkit-scrollbar {
  width: 4px;
}

.timeline-container::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.05);
  border-radius: 10px;
}

.timeline-container::-webkit-scrollbar-thumb {
  background: linear-gradient(45deg, #3b82f6, #8b5cf6);
  border-radius: 10px;
}

/* Responsive Adjustments */
@media (max-width: 1024px) {
  .timeline-sidebar {
    order: 2;
  }

  .main-content {
    order: 1;
  }
}

@media (max-width: 768px) {
  .timeline-card {
    padding: 1rem;
  }

  .hero-image {
    height: 200px;
  }

  .floating-badge {
    position: relative;
    margin-bottom: 1rem;
  }
}

/* Dark Mode Enhancements */
@media (prefers-color-scheme: dark) {
  .glass-card {
    border: 1px solid rgba(255, 255, 255, 0.1);
    background: rgba(0, 0, 0, 0.2);
  }

  .timeline-container::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.05);
  }
}

/* Auto-play Button Styles */
.auto-play-btn {
  transition: all 0.2s ease;
}

.auto-play-btn:hover {
  transform: scale(1.1);
  background: rgba(0, 0, 0, 0.1);
}

/* Line Clamp Utility */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Enhanced Shadows */
.shadow-timeline {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.shadow-timeline-lg {
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1),
    0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

.shadow-timeline-xl {
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1),
    0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* Gradient Text Animation */
.gradient-text {
  background: linear-gradient(45deg, #3b82f6, #8b5cf6, #ec4899, #f59e0b);
  background-size: 300% 300%;
  animation: gradientShift 4s ease infinite;
}

@keyframes gradientShift {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

/* Priority Badge Pulse */
.priority-badge {
  animation: priorityPulse 2s ease-in-out infinite;
}

@keyframes priorityPulse {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.8;
    transform: scale(1.05);
  }
}

/* Interactive Hover States */
.interactive-hover {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.interactive-hover:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

/* Accent Color Utilities */
.accent-blue {
  --tw-ring-color: #3b82f6;
  --tw-border-opacity: 1;
  border-color: rgb(59 130 246 / var(--tw-border-opacity));
}

.accent-emerald {
  --tw-ring-color: #10b981;
  --tw-border-opacity: 1;
  border-color: rgb(16 185 129 / var(--tw-border-opacity));
}

.accent-orange {
  --tw-ring-color: #f97316;
  --tw-border-opacity: 1;
  border-color: rgb(249 115 22 / var(--tw-border-opacity));
}

.accent-darkblue {
  --tw-ring-color: #1e40af;
  --tw-border-opacity: 1;
  border-color: rgb(30 64 175 / var(--tw-border-opacity));
}

.accent-indigo {
  --tw-ring-color: #6366f1;
  --tw-border-opacity: 1;
  border-color: rgb(99 102 241 / var(--tw-border-opacity));
}
